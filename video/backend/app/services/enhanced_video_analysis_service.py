"""
增强的视频分析服务
集成 Xinference LLM 和现有的视频分析能力
"""

import os
import json
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from loguru import logger

from app.core.config import settings
from app.services.video_analysis_service import VideoAnalysisService
from app.services.xinference_service import xinference_service
from app.services.enhanced_audio_service import EnhancedAudioService
from app.models.task import Video, VideoFrame, SceneChange
from app.utils.file_organization import VideoFileOrganizer


class EnhancedVideoAnalysisService:
    """增强的视频分析服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.base_service = VideoAnalysisService(db)
        self.audio_service = EnhancedAudioService(db)
        self.file_organizer = VideoFileOrganizer()
    
    async def comprehensive_video_analysis(
        self,
        video_id: int,
        use_xinference: Optional[bool] = None,
        analysis_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        综合视频分析
        
        Args:
            video_id: 视频ID
            use_xinference: 是否使用 Xinference
            analysis_options: 分析选项
            
        Returns:
            综合分析结果
        """
        try:
            # 决定是否使用 Xinference
            if use_xinference is None:
                use_xinference = settings.XINFERENCE_ENABLED and xinference_service.enabled
            
            analysis_options = analysis_options or {}
            
            logger.info(f"Starting comprehensive analysis for video {video_id}")
            
            # 基础视频分析
            basic_analysis = await self._perform_basic_analysis(video_id)
            
            # 音频转录
            audio_analysis = None
            if analysis_options.get("include_audio", True):
                audio_analysis = await self._perform_audio_analysis(
                    video_id, use_xinference
                )
            
            # 视觉内容分析
            visual_analysis = None
            if analysis_options.get("include_visual", True) and use_xinference:
                visual_analysis = await self._perform_visual_analysis(
                    video_id, analysis_options.get("visual_options", {})
                )
            
            # 场景分析
            scene_analysis = None
            if analysis_options.get("include_scenes", True):
                scene_analysis = await self._perform_scene_analysis(
                    video_id, use_xinference
                )
            
            # 生成综合报告
            comprehensive_report = await self._generate_comprehensive_report(
                video_id,
                basic_analysis,
                audio_analysis,
                visual_analysis,
                scene_analysis,
                use_xinference
            )
            
            return {
                "video_id": video_id,
                "analysis_method": "xinference" if use_xinference else "traditional",
                "basic_analysis": basic_analysis,
                "audio_analysis": audio_analysis,
                "visual_analysis": visual_analysis,
                "scene_analysis": scene_analysis,
                "comprehensive_report": comprehensive_report
            }
            
        except Exception as e:
            logger.error(f"Comprehensive video analysis failed for video {video_id}: {e}")
            raise
    
    async def _perform_basic_analysis(self, video_id: int) -> Dict[str, Any]:
        """执行基础视频分析"""
        try:
            # 使用现有的视频分析服务
            metadata = self.base_service.analyze_video_metadata(video_id)
            key_frames = self.base_service.extract_video_key_frames(video_id)
            
            return {
                "metadata": metadata,
                "key_frames_count": len(key_frames),
                "key_frames": key_frames[:10]  # 只返回前10个关键帧路径
            }
            
        except Exception as e:
            logger.error(f"Basic analysis failed: {e}")
            return {"error": str(e)}
    
    async def _perform_audio_analysis(
        self,
        video_id: int,
        use_xinference: bool
    ) -> Dict[str, Any]:
        """执行音频分析"""
        try:
            # 使用增强音频服务
            transcription = await self.audio_service.transcribe_video_audio(
                video_id, use_xinference
            )
            
            # 如果使用 Xinference，可以进行更深入的文本分析
            text_analysis = None
            if use_xinference and transcription.get("text"):
                text_analysis = await self._analyze_transcription_text(
                    transcription["text"]
                )
            
            return {
                "transcription": transcription,
                "text_analysis": text_analysis
            }
            
        except Exception as e:
            logger.error(f"Audio analysis failed: {e}")
            return {"error": str(e)}
    
    async def _perform_visual_analysis(
        self,
        video_id: int,
        visual_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行视觉内容分析"""
        try:
            # 获取关键帧
            key_frames = self.db.query(VideoFrame).filter(
                VideoFrame.video_id == video_id,
                VideoFrame.is_key_frame == True
            ).limit(visual_options.get("max_frames", 10)).all()
            
            if not key_frames:
                return {"error": "No key frames found"}
            
            # 分析每个关键帧
            frame_analyses = []
            for frame in key_frames:
                if frame.file_path and os.path.exists(frame.file_path):
                    analysis = await xinference_service.analyze_video_frame(
                        frame_path=frame.file_path,
                        question=visual_options.get(
                            "analysis_question",
                            "请详细描述这个视频帧的内容，包括场景、人物、动作和情感"
                        )
                    )
                    
                    frame_analyses.append({
                        "frame_id": frame.id,
                        "timestamp": frame.timestamp,
                        "analysis": analysis
                    })
            
            # 生成视觉内容摘要
            visual_summary = await self._generate_visual_summary(frame_analyses)
            
            return {
                "frame_analyses": frame_analyses,
                "visual_summary": visual_summary,
                "total_frames_analyzed": len(frame_analyses)
            }
            
        except Exception as e:
            logger.error(f"Visual analysis failed: {e}")
            return {"error": str(e)}
    
    async def _perform_scene_analysis(
        self,
        video_id: int,
        use_xinference: bool
    ) -> Dict[str, Any]:
        """执行场景分析"""
        try:
            # 获取场景变化数据
            scenes = self.db.query(SceneChange).filter(
                SceneChange.video_id == video_id
            ).order_by(SceneChange.scene_number).all()
            
            if not scenes:
                return {"error": "No scenes found"}
            
            scene_analyses = []
            
            if use_xinference:
                # 使用 LLM 分析每个场景
                for scene in scenes[:10]:  # 限制分析前10个场景
                    # 获取场景对应的关键帧
                    scene_frame = self.db.query(VideoFrame).filter(
                        VideoFrame.video_id == video_id,
                        VideoFrame.timestamp >= scene.start_time,
                        VideoFrame.timestamp <= scene.end_time,
                        VideoFrame.is_key_frame == True
                    ).first()
                    
                    if scene_frame and scene_frame.file_path:
                        analysis = await xinference_service.analyze_video_frame(
                            frame_path=scene_frame.file_path,
                            question="分析这个场景的内容、情绪和重要性"
                        )
                        
                        scene_analyses.append({
                            "scene_number": scene.scene_number,
                            "start_time": scene.start_time,
                            "end_time": scene.end_time,
                            "duration": scene.end_time - scene.start_time,
                            "analysis": analysis
                        })
            
            return {
                "total_scenes": len(scenes),
                "scene_analyses": scene_analyses,
                "average_scene_duration": sum(s.end_time - s.start_time for s in scenes) / len(scenes)
            }
            
        except Exception as e:
            logger.error(f"Scene analysis failed: {e}")
            return {"error": str(e)}
    
    async def _analyze_transcription_text(self, text: str) -> Dict[str, Any]:
        """分析转录文本"""
        try:
            # 生成摘要
            summary = await xinference_service.llm.generate_summary(
                content=text,
                summary_type="general"
            )
            
            # 提取关键词
            keywords = await xinference_service.llm.extract_keywords(
                text=text,
                max_keywords=10
            )
            
            # 情感分析（通过 LLM）
            emotion_analysis = await xinference_service.chat(
                messages=[{
                    "role": "user",
                    "content": f"请分析以下文本的情感倾向和主要情绪，以JSON格式返回：\n\n{text[:1000]}..."
                }]
            )
            
            return {
                "summary": summary,
                "keywords": keywords,
                "emotion_analysis": emotion_analysis,
                "text_length": len(text),
                "word_count": len(text.split())
            }
            
        except Exception as e:
            logger.error(f"Text analysis failed: {e}")
            return {"error": str(e)}
    
    async def _generate_visual_summary(
        self,
        frame_analyses: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """生成视觉内容摘要"""
        try:
            if not frame_analyses:
                return {"error": "No frame analyses provided"}
            
            # 收集所有分析结果
            all_analyses = [fa["analysis"]["analysis"] for fa in frame_analyses if "analysis" in fa["analysis"]]
            
            if not all_analyses:
                return {"error": "No valid analyses found"}
            
            # 使用 LLM 生成综合摘要
            combined_text = "\n\n".join(all_analyses)
            summary_prompt = f"""
            基于以下视频帧分析结果，生成一个综合的视觉内容摘要：
            
            {combined_text}
            
            请总结：
            1. 主要视觉元素和场景
            2. 人物和动作
            3. 整体风格和氛围
            4. 关键视觉特征
            """
            
            summary_response = await xinference_service.chat(
                messages=[{"role": "user", "content": summary_prompt}]
            )
            
            return {
                "summary": summary_response.get("choices", [{}])[0].get("message", {}).get("content", ""),
                "frames_analyzed": len(frame_analyses),
                "analysis_method": "llm_synthesis"
            }
            
        except Exception as e:
            logger.error(f"Visual summary generation failed: {e}")
            return {"error": str(e)}
    
    async def _generate_comprehensive_report(
        self,
        video_id: int,
        basic_analysis: Dict[str, Any],
        audio_analysis: Optional[Dict[str, Any]],
        visual_analysis: Optional[Dict[str, Any]],
        scene_analysis: Optional[Dict[str, Any]],
        use_xinference: bool
    ) -> Dict[str, Any]:
        """生成综合分析报告"""
        try:
            if not use_xinference:
                return {
                    "summary": "Traditional analysis completed",
                    "method": "basic"
                }
            
            # 构建报告提示
            report_sections = []
            
            if basic_analysis and "metadata" in basic_analysis:
                metadata = basic_analysis["metadata"]
                report_sections.append(f"视频基本信息：时长{metadata.get('duration', 'N/A')}秒，分辨率{metadata.get('resolution', 'N/A')}")
            
            if audio_analysis and "transcription" in audio_analysis:
                transcription = audio_analysis["transcription"]
                report_sections.append(f"音频内容：{transcription.get('text', '')[:200]}...")
            
            if visual_analysis and "visual_summary" in visual_analysis:
                visual_summary = visual_analysis["visual_summary"]
                report_sections.append(f"视觉内容：{visual_summary.get('summary', '')[:200]}...")
            
            if scene_analysis and "total_scenes" in scene_analysis:
                report_sections.append(f"场景分析：共{scene_analysis['total_scenes']}个场景")
            
            # 生成综合报告
            report_prompt = f"""
            基于以下视频分析结果，生成一份综合分析报告：
            
            {chr(10).join(report_sections)}
            
            请生成一份包含以下内容的报告：
            1. 视频概述
            2. 主要内容亮点
            3. 技术质量评估
            4. 内容特色分析
            5. 改进建议
            """
            
            report_response = await xinference_service.chat(
                messages=[{"role": "user", "content": report_prompt}]
            )
            
            return {
                "comprehensive_report": report_response.get("choices", [{}])[0].get("message", {}).get("content", ""),
                "analysis_components": {
                    "basic": basic_analysis is not None,
                    "audio": audio_analysis is not None,
                    "visual": visual_analysis is not None,
                    "scenes": scene_analysis is not None
                },
                "method": "ai_enhanced"
            }
            
        except Exception as e:
            logger.error(f"Comprehensive report generation failed: {e}")
            return {"error": str(e)}


# 创建服务实例的工厂函数
def create_enhanced_video_analysis_service(db: Session) -> EnhancedVideoAnalysisService:
    """创建增强视频分析服务实例"""
    return EnhancedVideoAnalysisService(db)
