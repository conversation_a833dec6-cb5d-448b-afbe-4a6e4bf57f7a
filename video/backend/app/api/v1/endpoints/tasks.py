"""
任务管理API端点
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List
from app.core.database import get_db
from app.core.dependencies import (
    get_current_active_user, require_task_create, require_task_read,
    require_task_update, require_task_delete, RequireOwnership
)
from app.models.task import Task, Video
from app.models.user import User
from app.services.task_service import TaskService
from app.core.config import settings
from app.tasks.video_tasks import process_task_videos
import json
import os
import uuid
import shutil
from pathlib import Path

router = APIRouter()


@router.get("", response_model=List[dict])
async def get_tasks(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_read)
):
    """获取任务列表"""
    tasks = db.query(Task).order_by(Task.created_at.desc()).all()
    return [
        {
            "id": task.id,
            "name": task.name,
            "description": task.description,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
            "video_count": len(task.videos)
        }
        for task in tasks
    ]


@router.post("", response_model=dict)
async def create_task(
    name: str = Form(...),
    description: str = Form(""),
    config: str = Form("{}"),
    synopsis: str = Form(""),
    genre: str = Form(""),
    theme: str = Form(""),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_create)
):
    """创建新任务"""
    try:
        config_dict = json.loads(config) if config else {}
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid config JSON")

    task = Task(
        name=name,
        description=description,
        config=config_dict,
        synopsis=synopsis if synopsis else None,
        genre=genre if genre else None,
        theme=theme if theme else None,
        status="pending"
    )

    db.add(task)
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "synopsis": task.synopsis,
        "genre": task.genre,
        "theme": task.theme,
        "status": task.status,
        "created_at": task.created_at.isoformat()
    }


@router.get("/{task_id}", response_model=dict)
async def get_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_read)
):
    """获取任务详情"""
    from app.models.task import AnalysisResult

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 构建视频信息，包含分析进度和摘要
    videos_data = []
    for video in task.videos:
        # 获取视频的分析结果
        analysis_results = db.query(AnalysisResult).filter(
            AnalysisResult.video_id == video.id
        ).all()

        # 计算分析进度
        analysis_progress = 0.0
        analysis_summary = None

        if analysis_results:
            # 根据分析步骤计算进度
            completed_steps = len(analysis_results)
            total_steps = 3  # basic_info, content_analysis, plot_analysis
            analysis_progress = min((completed_steps / total_steps) * 100, 100)

            # 从content_analysis结果中提取摘要信息
            for result in analysis_results:
                if result.step == "content_analysis" and result.result:
                    content_result = result.result
                    analysis_summary = {
                        "scene_count": len(content_result.get("scenes", [])),
                        "character_count": len(content_result.get("characters", [])),
                        "clip_count": len(content_result.get("objects", []))
                    }
                    break

        videos_data.append({
            "id": video.id,
            "filename": video.filename,
            "original_filename": video.original_filename,
            "duration": video.duration,
            "resolution": video.resolution,
            "fps": video.fps,
            "file_size": video.file_size,
            "thumbnail": f"/api/v1/videos/{video.id}/thumbnail" if video.key_frame_thumbnail_id else None,
            "status": video.status,
            "analysis_progress": analysis_progress,
            "analysis_summary": analysis_summary,
            "created_at": video.created_at.isoformat()
        })

    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "synopsis": task.synopsis,
        "genre": task.genre,
        "theme": task.theme,
        "status": task.status,
        "progress": task.progress,
        "config": task.config,
        "created_at": task.created_at.isoformat(),
        "updated_at": task.updated_at.isoformat(),
        "videos": videos_data
    }


@router.put("/{task_id}", response_model=dict)
async def update_task(
    task_id: int,
    name: str = Form(None),
    description: str = Form(None),
    status: str = Form(None),
    synopsis: str = Form(None),
    genre: str = Form(None),
    theme: str = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_update)
):
    """更新任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    if name is not None:
        task.name = name
    if description is not None:
        task.description = description
    if status is not None:
        task.status = status
    if synopsis is not None:
        task.synopsis = synopsis if synopsis else None
    if genre is not None:
        task.genre = genre if genre else None
    if theme is not None:
        task.theme = theme if theme else None

    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "name": task.name,
        "description": task.description,
        "synopsis": task.synopsis,
        "genre": task.genre,
        "theme": task.theme,
        "status": task.status,
        "updated_at": task.updated_at.isoformat()
    }


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_delete)
):
    """删除任务"""
    from app.services.task_service import TaskService

    task_service = TaskService(db)
    success = task_service.delete_task(task_id)

    if not success:
        raise HTTPException(status_code=404, detail="Task not found")

    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/upload")
async def upload_video(
    task_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(require_task_update)
):
    """上传视频到任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查文件类型
    if not file.filename:
        raise HTTPException(status_code=400, detail="No filename provided")

    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.VIDEO_FORMATS:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported formats: {', '.join(settings.VIDEO_FORMATS)}"
        )

    # 检查文件大小
    file_size = 0
    if hasattr(file, 'size') and file.size:
        file_size = file.size
        if file_size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE // (1024*1024*1024)}GB"
            )

    try:
        # 先创建Video记录以获取video_id
        video = Video(
            task_id=task_id,
            filename=file.filename,  # 保持原始文件名
            original_filename=file.filename,
            file_path="",  # 临时为空，稍后更新
            file_size=file_size,
            status="uploading"
        )

        db.add(video)
        db.commit()
        db.refresh(video)

        # 使用video_id创建组织化的目录结构
        from app.utils.file_organization import file_organizer
        file_organizer.create_video_directory_structure(video.id)
        
        # 获取正确的文件路径
        final_file_path = file_organizer.get_original_video_path(video.id, file.filename)

        # 保存文件到正确位置
        with open(final_file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 获取实际文件大小
        if file_size == 0:
            file_size = os.path.getsize(final_file_path)

        # 更新Video记录
        video.file_path = str(final_file_path)
        video.file_size = file_size
        video.status = "uploaded"

        # 检查并更新任务状态
        # 如果任务不是处于活跃状态（pending或processing），则重置为pending
        if task.status not in ["pending", "processing"]:
            task.status = "pending"
            task.progress = 0.0  # 重置进度
            task.celery_task_id = None  # 清除之前的任务ID

        db.commit()
        db.refresh(video)
        db.refresh(task)

        return {
            "id": video.id,
            "message": "Video uploaded successfully",
            "filename": video.filename,
            "original_filename": video.original_filename,
            "size": video.file_size,
            "status": video.status,
            "task_status": task.status  # 返回更新后的任务状态
        }

    except Exception as e:
        # 如果出错，删除已上传的文件和数据库记录
        if 'final_file_path' in locals() and os.path.exists(final_file_path):
            os.remove(final_file_path)
        if 'video' in locals():
            db.delete(video)
            db.commit()
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")


@router.post("/{task_id}/start")
async def start_task_processing(task_id: int, db: Session = Depends(get_db)):
    """开始任务处理"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务状态
    if task.status not in ["pending", "paused"]:
        raise HTTPException(
            status_code=400,
            detail=f"Task cannot be started. Current status: {task.status}"
        )

    # 检查是否有视频
    video_count = db.query(Video).filter(Video.task_id == task_id).count()
    if video_count == 0:
        raise HTTPException(
            status_code=400,
            detail="请先上传视频到任务"
        )

    from app.tasks.video_tasks import process_task_videos
    celery_task = process_task_videos.delay(task_id)

    # 更新任务状态，保存Celery任务ID
    task.status = "processing"
    task.progress = 0.0
    task.celery_task_id = celery_task.id
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "status": task.status,
        "progress": task.progress,
        "video_count": video_count,
        "celery_task_id": celery_task.id,
        "message": "Task processing started"
    }


@router.post("/{task_id}/pause")
async def pause_task_processing(task_id: int, db: Session = Depends(get_db)):
    """暂停任务处理"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务状态
    if task.status != "processing":
        raise HTTPException(
            status_code=400,
            detail=f"Task cannot be paused. Current status: {task.status}"
        )

    # 如果有Celery任务ID，尝试撤销任务
    if task.celery_task_id:
        try:
            from app.tasks.celery import celery
            celery.control.revoke(task.celery_task_id, terminate=True)
        except Exception:
            # 即使撤销失败，也继续更新状态
            pass

    # 更新任务状态为暂停
    task.status = "paused"
    task.celery_task_id = None  # 清除Celery任务ID
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "status": task.status,
        "progress": task.progress,
        "message": "Task processing paused"
    }


@router.post("/{task_id}/retry")
async def retry_task(task_id: int, db: Session = Depends(get_db)):
    """重试任务"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    # 检查任务状态
    if task.status != "failed":
        raise HTTPException(
            status_code=400,
            detail=f"Task cannot be retried. Current status: {task.status}"
        )
    # 检查是否有视频
    video_count = db.query(Video).filter(Video.task_id == task_id).count()
    if video_count == 0:
        raise HTTPException(
            status_code=400,
            detail="请先上传视频到任务"
        )
    # 重置任务状态
    task.status = "pending"
    task.progress = 0.0
    task.error_message = None
    task.celery_task_id = None  # 清除Celery任务ID
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "status": task.status,
        "progress": task.progress,
        "message": "Task retry started"
    }


@router.get("/{task_id}/logs")
async def get_task_logs(task_id: int, db: Session = Depends(get_db)):
    """获取任务处理日志"""
    from app.models.task import AnalysisResult

    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 获取所有相关视频的分析结果作为日志
    logs = []

    # 添加任务创建日志
    logs.append({
        "id": f"task_{task.id}_created",
        "level": "info",
        "message": f"任务 '{task.name}' 已创建",
        "timestamp": task.created_at.isoformat()
    })

    # 添加视频上传日志
    for video in task.videos:
        logs.append({
            "id": f"video_{video.id}_uploaded",
            "level": "info",
            "message": f"视频 '{video.original_filename}' 上传完成",
            "timestamp": video.created_at.isoformat()
        })

        # 添加分析结果日志
        analysis_results = db.query(AnalysisResult).filter(
            AnalysisResult.video_id == video.id
        ).order_by(AnalysisResult.created_at).all()

        for result in analysis_results:
            step_names = {
                "basic_info": "基础信息分析",
                "content_analysis": "内容分析",
                "plot_analysis": "情节分析"
            }
            step_name = step_names.get(result.step, result.step)

            logs.append({
                "id": f"analysis_{result.id}",
                "level": "success",
                "message": f"视频 '{video.original_filename}' {step_name}完成 (置信度: {result.confidence:.2f})",
                "timestamp": result.created_at.isoformat()
            })

    # 按时间倒序排序
    logs.sort(key=lambda x: x["timestamp"], reverse=True)

    return logs


# MiniCPM-V-4 Task Endpoints

@router.post("/{task_id}/start-with-minicpm")
async def start_task_processing_with_minicpm(task_id: int, db: Session = Depends(get_db)):
    """开始任务处理（包含MiniCPM-V-4分析）"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # 检查任务状态
    if task.status not in ["pending", "paused"]:
        raise HTTPException(
            status_code=400,
            detail=f"Task cannot be started. Current status: {task.status}"
        )

    # 检查是否有视频
    video_count = db.query(Video).filter(Video.task_id == task_id).count()
    if video_count == 0:
        raise HTTPException(
            status_code=400,
            detail="请先上传视频到任务"
        )

    from app.tasks.video_tasks import process_task_videos_with_minicpm
    celery_task = process_task_videos_with_minicpm.delay(task_id)

    # 更新任务状态，保存Celery任务ID
    task.status = "processing"
    task.progress = 0.0
    task.celery_task_id = celery_task.id
    db.commit()
    db.refresh(task)

    return {
        "id": task.id,
        "status": task.status,
        "progress": task.progress,
        "video_count": video_count,
        "celery_task_id": celery_task.id,
        "message": "Task processing started with MiniCPM-V-4 analysis"
    }


@router.post("/{task_id}/videos/{video_id}/minicpm-analysis")
async def start_single_video_minicpm_analysis(
    task_id: int, 
    video_id: int, 
    db: Session = Depends(get_db)
):
    """为单个视频启动MiniCPM-V-4分析"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks import process_single_video_with_minicpm_analysis
    celery_task = process_single_video_with_minicpm_analysis.delay(video_id)

    # 更新视频状态
    video.status = "analyzing"
    db.commit()

    return {
        "task_id": task_id,
        "video_id": video_id,
        "celery_task_id": celery_task.id,
        "message": "MiniCPM-V-4 analysis started for video"
    }


@router.post("/{task_id}/videos/{video_id}/minicpm-scene-analysis")
async def start_video_scene_analysis_with_minicpm(
    task_id: int,
    video_id: int,
    analysis_type: str = Form("content"),
    db: Session = Depends(get_db)
):
    """为单个视频启动MiniCPM-V-4场景分析"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks_minicpm import analyze_video_with_minicpm_scenes
    celery_task = analyze_video_with_minicpm_scenes.delay(video_id, analysis_type)

    return {
        "task_id": task_id,
        "video_id": video_id,
        "analysis_type": analysis_type,
        "celery_task_id": celery_task.id,
        "message": f"MiniCPM-V-4 scene analysis ({analysis_type}) started for video"
    }


@router.post("/{task_id}/videos/{video_id}/minicpm-scene-changes")
async def start_video_scene_changes_with_minicpm(
    task_id: int,
    video_id: int,
    comparison_threshold: int = Form(5),
    db: Session = Depends(get_db)
):
    """为单个视频启动MiniCPM-V-4场景变化检测"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks_minicpm import detect_scene_changes_with_minicpm
    celery_task = detect_scene_changes_with_minicpm.delay(video_id, comparison_threshold)

    return {
        "task_id": task_id,
        "video_id": video_id,
        "comparison_threshold": comparison_threshold,
        "celery_task_id": celery_task.id,
        "message": "MiniCPM-V-4 scene change detection started for video"
    }


@router.post("/{task_id}/videos/{video_id}/minicpm-content-summary")
async def start_video_content_summary_with_minicpm(
    task_id: int,
    video_id: int,
    db: Session = Depends(get_db)
):
    """为单个视频启动MiniCPM-V-4内容摘要生成"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks_minicpm import generate_video_content_summary_with_minicpm
    celery_task = generate_video_content_summary_with_minicpm.delay(video_id)

    return {
        "task_id": task_id,
        "video_id": video_id,
        "celery_task_id": celery_task.id,
        "message": "MiniCPM-V-4 content summary generation started for video"
    }


@router.get("/{task_id}/videos/{video_id}/minicpm-results")
async def get_video_minicpm_analysis_results(
    task_id: int,
    video_id: int,
    db: Session = Depends(get_db)
):
    """获取视频的MiniCPM-V-4分析结果"""
    from app.models.task import AnalysisResult
    
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    # 获取所有MiniCPM-V-4相关的分析结果（包括字幕集成）
    minicpm_steps = [
        "minicpm_scene_analysis",
        "minicpm_scene_changes", 
        "minicpm_content_summary",
        "minicpm_scene_analysis_with_subtitles",
        "minicpm_content_summary_with_subtitles",
        "minicpm_dialogue_scene_alignment"
    ]
    
    results = db.query(AnalysisResult).filter(
        AnalysisResult.video_id == video_id,
        AnalysisResult.step.in_(minicpm_steps)
    ).all()

    analysis_results = {}
    for result in results:
        analysis_results[result.step] = {
            "id": result.id,
            "result": result.result,
            "confidence": result.confidence,
            "processing_time": result.processing_time,
            "created_at": result.created_at.isoformat()
        }

    return {
        "task_id": task_id,
        "video_id": video_id,
        "video_filename": video.filename,
        "analysis_results": analysis_results,
        "total_results": len(analysis_results),
        "subtitle_integration_available": any("with_subtitles" in step for step in analysis_results.keys())
    }


# MiniCPM-V-4 with Subtitle Integration Task Endpoints

@router.post("/{task_id}/videos/{video_id}/minicpm-scenes-with-subtitles")
async def start_video_scene_analysis_with_subtitles(
    task_id: int,
    video_id: int,
    analysis_type: str = Form("content"),
    context_window: float = Form(10.0),
    db: Session = Depends(get_db)
):
    """为单个视频启动MiniCPM-V-4场景分析（含字幕集成）"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks_minicpm import analyze_video_scenes_with_subtitles
    celery_task = analyze_video_scenes_with_subtitles.delay(video_id, analysis_type, context_window)

    return {
        "task_id": task_id,
        "video_id": video_id,
        "analysis_type": analysis_type,
        "context_window": context_window,
        "celery_task_id": celery_task.id,
        "message": f"MiniCPM-V-4 scene analysis with subtitles ({analysis_type}) started for video"
    }


@router.post("/{task_id}/videos/{video_id}/minicpm-summary-with-subtitles")
async def start_video_content_summary_with_subtitles(
    task_id: int,
    video_id: int,
    db: Session = Depends(get_db)
):
    """为单个视频启动MiniCPM-V-4内容摘要生成（含字幕集成）"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks_minicpm import generate_video_content_summary_with_subtitles
    celery_task = generate_video_content_summary_with_subtitles.delay(video_id)

    return {
        "task_id": task_id,
        "video_id": video_id,
        "celery_task_id": celery_task.id,
        "message": "MiniCPM-V-4 content summary with subtitles generation started for video"
    }


@router.post("/{task_id}/videos/{video_id}/minicpm-dialogue-alignment")
async def start_dialogue_scene_alignment_analysis(
    task_id: int,
    video_id: int,
    db: Session = Depends(get_db)
):
    """为单个视频启动对话场景对齐分析"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks_minicpm import analyze_dialogue_scene_alignment
    celery_task = analyze_dialogue_scene_alignment.delay(video_id)

    return {
        "task_id": task_id,
        "video_id": video_id,
        "celery_task_id": celery_task.id,
        "message": "Dialogue-scene alignment analysis started for video"
    }


@router.post("/{task_id}/videos/{video_id}/minicpm-comprehensive-with-subtitles")
async def start_comprehensive_analysis_with_subtitles(
    task_id: int,
    video_id: int,
    db: Session = Depends(get_db)
):
    """为单个视频启动MiniCPM-V-4综合分析（含字幕集成）"""
    task = db.query(Task).filter(Task.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    
    video = db.query(Video).filter(Video.id == video_id, Video.task_id == task_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found in this task")

    from app.tasks.video_tasks_minicpm import comprehensive_minicpm_analysis_with_subtitles
    celery_task = comprehensive_minicpm_analysis_with_subtitles.delay(video_id)

    return {
        "task_id": task_id,
        "video_id": video_id,
        "celery_task_id": celery_task.id,
        "message": "MiniCPM-V-4 comprehensive analysis with subtitles started for video"
    }